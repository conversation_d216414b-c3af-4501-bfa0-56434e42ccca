import React from 'react';
import { StyleProp, StyleSheet, View, ViewStyle } from 'react-native';

interface ProgressBarProps {
  progress: number; // Value from 0 to 1
  progressColor?: string;
  trackColor?: string;
  style?: StyleProp<ViewStyle>;
}

export const ProgressBar: React.FC<ProgressBarProps> = ({
  progress,
  progressColor = '#0A7EA4',
  trackColor = '#E5E5EA',
  style,
}) => {
  // Ensure progress is between 0 and 1
  const progressValue = Math.min(Math.max(progress, 0), 1);

  return (
    <View style={[styles.track, { backgroundColor: trackColor }, style]}>
      <View
        style={[
          styles.progress,
          {
            backgroundColor: progressColor,
            width: `${progressValue * 100}%`,
          },
        ]}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  track: {
    height: 6,
    borderRadius: 3,
    overflow: 'hidden',
    width: '100%',
  },
  progress: {
    height: '100%',
    borderRadius: 3,
  },
}); 