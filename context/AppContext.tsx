import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Localization from 'expo-localization';
import React, { createContext, useContext, useEffect, useState } from 'react';
import { useColorScheme as useDeviceColorScheme } from 'react-native';

interface AppContextType {
  isDarkMode: boolean;
  isAIEnabled: boolean;
  language: string;
  isLoading: boolean;
  error: string | null;
  toggleDarkMode: () => Promise<void>;
  toggleAIEnabled: () => Promise<void>;
  setLanguage: (lang: string) => Promise<void>;
  clearError: () => void;
}

const AppContext = createContext<AppContextType | undefined>(undefined);

export const AppProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const deviceColorScheme = useDeviceColorScheme();
  const [isDarkMode, setIsDarkMode] = useState(true); // Default to dark mode
  const [isAIEnabled, setIsAIEnabled] = useState(false);
  const [language, setLanguageState] = useState(Localization.locale.split('-')[0] === 'hi' ? 'hi' : 'en');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load saved preferences from AsyncStorage
  useEffect(() => {
    const loadPreferences = async () => {
      try {
        setIsLoading(true);
        const [storedDarkMode, storedAIMode, storedLanguage] = await Promise.all([
          AsyncStorage.getItem('isDarkMode'),
          AsyncStorage.getItem('isAIEnabled'),
          AsyncStorage.getItem('language'),
        ]);

        // Only update if values exist in storage
        if (storedDarkMode !== null) {
          setIsDarkMode(storedDarkMode === 'true');
        }
        if (storedAIMode !== null) {
          setIsAIEnabled(storedAIMode === 'true');
        }
        if (storedLanguage !== null) {
          setLanguageState(storedLanguage);
        }
      } catch (error) {
        console.error('Failed to load preferences', error);
        setError('Failed to load preferences. Please restart the app.');
      } finally {
        setIsLoading(false);
      }
    };

    loadPreferences();
  }, []);

  // Toggle dark mode
  const toggleDarkMode = async () => {
    try {
      const newMode = !isDarkMode;
      setIsDarkMode(newMode);
      await AsyncStorage.setItem('isDarkMode', String(newMode));
    } catch (error) {
      console.error('Failed to save dark mode preference', error);
      setError('Failed to save dark mode preference.');
      // Revert the UI change since the save failed
      setIsDarkMode(isDarkMode);
    }
  };

  // Toggle AI mode
  const toggleAIEnabled = async () => {
    try {
      const newMode = !isAIEnabled;
      setIsAIEnabled(newMode);
      await AsyncStorage.setItem('isAIEnabled', String(newMode));
    } catch (error) {
      console.error('Failed to save AI mode preference', error);
      setError('Failed to save AI mode preference.');
      // Revert the UI change since the save failed
      setIsAIEnabled(isAIEnabled);
    }
  };

  // Set language
  const setLanguage = async (lang: string) => {
    try {
      setLanguageState(lang);
      await AsyncStorage.setItem('language', lang);
    } catch (error) {
      console.error('Failed to save language preference', error);
      setError('Failed to save language preference.');
      // Don't revert the UI change for language as it's more disruptive
    }
  };

  // Clear error
  const clearError = () => {
    setError(null);
  };

  return (
    <AppContext.Provider
      value={{
        isDarkMode,
        isAIEnabled,
        language,
        isLoading,
        error,
        toggleDarkMode,
        toggleAIEnabled,
        setLanguage,
        clearError,
      }}
    >
      {children}
    </AppContext.Provider>
  );
};

export const useAppContext = (): AppContextType => {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useAppContext must be used within an AppProvider');
  }
  return context;
}; 