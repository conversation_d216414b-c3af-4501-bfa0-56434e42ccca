import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

// English translations
const enTranslations = {
  common: {
    welcome: 'Welcome to StoragePro',
    continue: 'Continue',
    cancel: 'Cancel',
    save: 'Save',
    delete: 'Delete',
    settings: 'Settings',
    back: 'Back',
  },
  languageSelection: {
    chooseLanguage: 'Choose your language',
    english: 'English',
    hindi: 'Hindi',
  },
  home: {
    title: 'Storage',
    usedStorage: 'Used Space',
    freeStorage: 'Available Space',
    quickActions: 'Quick Actions',
    analyzeStorage: 'Smart Analysis',
    cleanStorage: 'Deep Clean',
    compressFiles: 'Optimize Files',
    aiAssistant: 'Smart Assistant',
  },
  analyze: {
    title: 'Analysis',
    largeFiles: 'Space Hogs',
    unusedApps: 'Dormant Apps',
    duplicateFiles: 'Duplicates',
    oldFiles: 'Outdated Files',
    scanNow: 'Scan Now',
    analyzeWithAI: 'Analyze with AI',
    scanning: 'Scanning...',
    startScanText: 'Tap "Scan Now" to analyze your storage',
    noResults: 'No results found',
    scanError: 'Scan Error',
    scanErrorMessage: 'There was an error scanning your storage. Please try again.',
    aiDisabled: 'AI Features Disabled',
    enableAI: 'Enable AI features in settings to use this feature',
    aiRecommendations: 'AI Recommendations',
    aiError: 'AI Analysis Error',
    aiErrorMessage: 'There was an error analyzing your storage. Please try again.',
    featureNotAvailable: 'Feature Not Available',
    featureComingSoon: 'This feature is coming soon!',
    optimizeNow: 'Optimize Now'
  },
  clean: {
    title: 'Clean',
    junkFiles: 'Junk Files',
    cacheCleaner: 'Cache Cleaner',
    appData: 'App Data',
    downloadedFiles: 'Downloads',
    selectAll: 'Select All',
    cleanSelected: 'Clean Selected',
  },
  compress: {
    title: 'Compress',
    images: 'Photos',
    videos: 'Videos',
    documents: 'Documents',
    compressionLevel: 'Compression Level',
    light: 'Light',
    medium: 'Medium',
    heavy: 'Heavy',
    selectAll: 'Select All',
    deselectAll: 'Deselect All',
    noFilesSelected: 'No Files Selected',
    pleaseSelectFiles: 'Please select files to compress',
    compressSelectedFiles: 'Compress Selected Files',
    compressionWarning: 'Are you sure you want to compress {{count}} files? This may reduce quality.',
    compress: 'Compress',
    compressionComplete: 'Compression Complete',
    compressionStats: 'Successfully compressed {{count}} files.\nEstimated space saved: {{space}}.',
    error: 'Error',
    errorMessage: 'Failed to compress files.',
    compressNow: 'Compress Now',
    highCompressionNote: 'Higher compression, lower quality',
    lowCompressionNote: 'Lower compression, higher quality',
    balancedCompressionNote: 'Balanced compression and quality',
    load: 'Load',
    noFilesLoaded: 'No {{category}} loaded. Tap the button above to load {{category}}.',
    selectedCount: '{{selected}} / {{total}} selected'
  },
  settings: {
    title: 'Settings',
    language: 'Language',
    darkMode: 'Dark Mode',
    notifications: 'Notifications',
    about: 'About',
    privacyPolicy: 'Privacy Policy',
    termsOfService: 'Terms of Service',
    aiMode: 'AI Mode',
    changeLanguage: 'Change Language',
    appearance: 'Appearance',
    features: 'Features',
    aiFeatures: 'AI Features',
    version: 'App Version',
  },
  ai: {
    title: 'Smart Assistant',
    suggestion: 'Insights',
    optimize: 'Auto-Optimize',
    help: 'Help',
  },
};

// Hindi translations
const hiTranslations = {
  common: {
    welcome: 'स्टोरेजप्रो में आपका स्वागत है',
    continue: 'जारी रखें',
    cancel: 'रद्द करें',
    save: 'सहेजें',
    delete: 'हटाएं',
    settings: 'सेटिंग्स',
    back: 'वापस',
  },
  languageSelection: {
    chooseLanguage: 'अपनी भाषा चुनें',
    english: 'अंग्रेज़ी',
    hindi: 'हिंदी',
  },
  home: {
    title: 'स्टोरेज',
    usedStorage: 'उपयोग की गई जगह',
    freeStorage: 'उपलब्ध जगह',
    quickActions: 'त्वरित कार्रवाई',
    analyzeStorage: 'स्मार्ट विश्लेषण',
    cleanStorage: 'डीप क्लीन',
    compressFiles: 'फाइलें ऑप्टिमाइज़ करें',
    aiAssistant: 'स्मार्ट सहायक',
  },
  analyze: {
    title: 'विश्लेषण',
    largeFiles: 'अधिक जगह लेने वाली फाइलें',
    unusedApps: 'निष्क्रिय ऐप्स',
    duplicateFiles: 'डुप्लिकेट',
    oldFiles: 'पुरानी फाइलें',
    scanNow: 'अभी स्कैन करें',
    analyzeWithAI: 'AI के साथ विश्लेषण',
    scanning: 'स्कैनिंग...',
    startScanText: 'अपने स्टोरेज को विश्लेषण करने के लिए "अभी स्कैन करें" टैप करें',
    noResults: 'कोई परिणाम नहीं मिला',
    scanError: 'स्कैन त्रुटि',
    scanErrorMessage: 'स्टोरेज स्कैन करते समय एक त्रुटि हुई। कृपया पुनः प्रयास करें।',
    aiDisabled: 'AI सुविधाएँ अक्षम',
    enableAI: 'इस सुविधा का उपयोग करने के लिए सेटिंग्स में AI सुविधाएँ सक्षम करें',
    aiRecommendations: 'AI सुझाव',
    aiError: 'AI विश्लेषण त्रुटि',
    aiErrorMessage: 'स्टोरेज विश्लेषण करते समय एक त्रुटि हुई। कृपया पुनः प्रयास करें।',
    featureNotAvailable: 'सुविधा उपलब्ध नहीं',
    featureComingSoon: 'यह सुविधा आगामी है!',
    optimizeNow: 'अभी ऑप्टिमाइज़ करें'
  },
  clean: {
    title: 'क्लीन',
    junkFiles: 'जंक फाइलें',
    cacheCleaner: 'कैश क्लीनर',
    appData: 'ऐप डेटा',
    downloadedFiles: 'डाउनलोड्स',
    selectAll: 'सभी चुनें',
    cleanSelected: 'चयनित साफ करें',
  },
  compress: {
    title: 'कंप्रेस',
    images: 'फोटो',
    videos: 'वीडियो',
    documents: 'दस्तावेज़',
    compressionLevel: 'ऑप्टिमाइजेशन स्तर',
    light: 'हल्का',
    medium: 'संतुलित',
    heavy: 'अधिकतम',
    selectAll: 'सभी चुनें',
    deselectAll: 'सभी हटाएं',
    noFilesSelected: 'कोई फाइलें नहीं चुनी गई',
    pleaseSelectFiles: 'कृपया फाइलें चुनें',
    compressSelectedFiles: 'चयनित फाइलें संपीड़ित करें',
    compressionWarning: 'क्या आप निश्चित है कि आप इसे संपीड़ित करना चाहते हैं {{count}} फाइलें? इससे गुणवत्ता कम हो सकती है।',
    compress: 'संपीड़ित करें',
    compressionComplete: 'संपीड़न पूर्ण',
    compressionStats: 'सफलतापूर्वक {{count}} फाइलें संपीड़ित करें।\nअनुमानित स्थान बचाया: {{space}}.',
    error: 'त्रुटि',
    errorMessage: 'फाइलें संपीड़ित नहीं कर सकते',
    compressNow: 'अभी संपीड़ित करें',
    highCompressionNote: 'उच्च संपीड़न, कम गुणवत्ता',
    lowCompressionNote: 'कम संपीड़न, उच्च गुणवत्ता',
    balancedCompressionNote: 'संतुलित संपीड़न और गुणवत्ता',
    load: 'लोड',
    noFilesLoaded: 'कोई {{category}} लोड नहीं किया गया। ऊपर दिए गए बटन को टैप करके {{category}} लोड करें।',
    selectedCount: '{{selected}} / {{total}} चयनित'
  },
  settings: {
    title: 'सेटिंग्स',
    language: 'भाषा',
    darkMode: 'डार्क मोड',
    notifications: 'सूचनाएं',
    about: 'के बारे में',
    privacyPolicy: 'गोपनीयता नीति',
    termsOfService: 'सेवा की शर्तें',
    aiMode: 'AI मोड',
    changeLanguage: 'भाषा बदलें',
    appearance: 'दिखावट',
    features: 'सुविधाएँ',
    aiFeatures: 'AI सुविधाएँ',
    version: 'एप्प वर्शन',
  },
  ai: {
    title: 'स्मार्ट सहायक',
    suggestion: 'अंतर्दृष्टि',
    optimize: 'स्वचालित-अनुकूलन',
    help: 'मदद',
  },
};

// Initialize i18n
i18n.use(initReactI18next).init({
  resources: {
    en: enTranslations,
    hi: hiTranslations,
  },
  lng: 'en',
  fallbackLng: 'en',
  interpolation: {
    escapeValue: false,
  },
});

export default i18n; 