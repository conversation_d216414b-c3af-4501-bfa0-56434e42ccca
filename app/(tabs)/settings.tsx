import { router } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Alert, Linking, SafeAreaView, ScrollView, StyleSheet, Switch, Text, TouchableOpacity, View } from 'react-native';

import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useAppContext } from '@/context/AppContext';
import { requestStoragePermissions } from '@/utils/storageUtils';

export default function SettingsTab() {
  const { t } = useTranslation();
  const { isDarkMode, toggleDarkMode, isAIEnabled, toggleAIEnabled, language } = useAppContext();
  const [protectSystemFiles, setProtectSystemFiles] = useState(true);
  const [confirmBeforeDelete, setConfirmBeforeDelete] = useState(true);
  const [advancedModeEnabled, setAdvancedModeEnabled] = useState(false);
  const [hasStoragePermissions, setHasStoragePermissions] = useState<boolean | null>(null);
  
  // Get color scheme based on dark mode
  const colorScheme = isDarkMode ? 'dark' : 'light';
  const backgroundColor = Colors[colorScheme].background;
  const textColor = Colors[colorScheme].text;
  const cardColor = isDarkMode ? '#1E1E1E' : '#FFFFFF';

  // Check if we have storage permissions
  useEffect(() => {
    const checkPermissions = async () => {
      const permissions = await requestStoragePermissions();
      setHasStoragePermissions(permissions);
    };
    
    checkPermissions();
  }, []);

  const toggleProtectSystemFiles = () => {
    if (protectSystemFiles && !advancedModeEnabled) {
      Alert.alert(
        "Security Warning",
        "Disabling system file protection may allow deleting critical files needed for proper operation. Are you sure you want to proceed?",
        [
          { text: "Cancel", style: "cancel" },
          { 
            text: "Proceed", 
            style: "destructive",
            onPress: () => setProtectSystemFiles(false)
          }
        ]
      );
    } else {
      setProtectSystemFiles(!protectSystemFiles);
    }
  };

  const requestPermissions = async () => {
    const granted = await requestStoragePermissions();
    setHasStoragePermissions(granted);
    
    if (!granted) {
      Alert.alert(
        "Permissions Required",
        "Storage access permissions are needed for this app to function correctly. Please enable them in your device settings.",
        [
          { text: "Cancel", style: "cancel" },
          { 
            text: "Open Settings", 
            onPress: () => Linking.openSettings()
          }
        ]
      );
    } else {
      Alert.alert(
        "Permissions Granted",
        "Storage access permissions have been granted. All app features should now work correctly."
      );
    }
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: textColor }]}>Settings</Text>
      </View>

      <ScrollView contentContainerStyle={styles.content}>
        {/* Appearance Section */}
        <Text style={[styles.sectionTitle, { color: textColor }]}>Display</Text>
        <View style={[styles.card, { backgroundColor: cardColor }]}>
          <View style={styles.settingRow}>
            <View style={styles.settingLabelContainer}>
              <Text style={[styles.settingText, { color: textColor }]}>Dark Mode</Text>
              <Text style={[styles.settingDescription, { color: isDarkMode ? '#9BA1A6' : '#687076' }]}>
                Enable dark theme for night usage
              </Text>
            </View>
            <Switch
              value={isDarkMode}
              onValueChange={toggleDarkMode}
              trackColor={{ false: '#767577', true: Colors[colorScheme].tint }}
              thumbColor="#FFFFFF"
            />
          </View>
        </View>
        
        {/* Permissions Section */}
        <Text style={[styles.sectionTitle, { color: textColor }]}>Permissions</Text>
        <View style={[styles.card, { backgroundColor: cardColor }]}>
          <View style={styles.settingRow}>
            <View style={styles.settingLabelContainer}>
              <Text style={[styles.settingText, { color: textColor }]}>Storage Access</Text>
              <Text style={[styles.settingDescription, { color: isDarkMode ? '#9BA1A6' : '#687076' }]}>
                Required for analyzing and managing files
              </Text>
            </View>
            {hasStoragePermissions !== null && (
              <View style={styles.permissionStatus}>
                <Text style={[
                  styles.permissionStatusText, 
                  { 
                    color: hasStoragePermissions ? '#34C759' : '#FF3B30',
                    marginRight: 8
                  }
                ]}>
                  {hasStoragePermissions ? 'Granted' : 'Denied'}
                </Text>
                {!hasStoragePermissions && (
                  <TouchableOpacity
                    style={[styles.permissionButton, { backgroundColor: '#0A7EA4' }]}
                    onPress={requestPermissions}
                  >
                    <Text style={styles.permissionButtonText}>Request</Text>
                  </TouchableOpacity>
                )}
              </View>
            )}
          </View>
        </View>

        {/* Security Section */}
        <Text style={[styles.sectionTitle, { color: textColor }]}>Security & Protection</Text>
        <View style={[styles.card, { backgroundColor: cardColor }]}>
          <View style={styles.settingRow}>
            <View style={styles.settingLabelContainer}>
              <Text style={[styles.settingText, { color: textColor }]}>Protect System Files</Text>
              <Text style={[styles.settingDescription, { color: isDarkMode ? '#9BA1A6' : '#687076' }]}>
                Prevent deletion of essential files
              </Text>
            </View>
            <Switch
              value={protectSystemFiles}
              onValueChange={toggleProtectSystemFiles}
              trackColor={{ false: '#767577', true: '#34C759' }}
              thumbColor="#FFFFFF"
            />
          </View>
          
          <View style={styles.divider} />
          
          <View style={styles.settingRow}>
            <View style={styles.settingLabelContainer}>
              <Text style={[styles.settingText, { color: textColor }]}>Confirm Deletions</Text>
              <Text style={[styles.settingDescription, { color: isDarkMode ? '#9BA1A6' : '#687076' }]}>
                Always ask before deleting files
              </Text>
            </View>
            <Switch
              value={confirmBeforeDelete}
              onValueChange={setConfirmBeforeDelete}
              trackColor={{ false: '#767577', true: '#34C759' }}
              thumbColor="#FFFFFF"
            />
          </View>
          
          <View style={styles.divider} />
          
          <View style={styles.settingRow}>
            <View style={styles.settingLabelContainer}>
              <Text style={[styles.settingText, { color: textColor }]}>Advanced Mode</Text>
              <Text style={[styles.settingDescription, { color: isDarkMode ? '#9BA1A6' : '#687076' }]}>
                For experienced users only
              </Text>
            </View>
            <Switch
              value={advancedModeEnabled}
              onValueChange={setAdvancedModeEnabled}
              trackColor={{ false: '#767577', true: '#FF9500' }}
              thumbColor="#FFFFFF"
            />
          </View>
        </View>

        {/* Language Section */}
        <Text style={[styles.sectionTitle, { color: textColor }]}>Language & Region</Text>
        <View style={[styles.card, { backgroundColor: cardColor }]}>
          <TouchableOpacity 
            style={styles.settingRow}
            onPress={() => router.push("/language-selection")}
          >
            <Text style={[styles.settingText, { color: textColor }]}>App Language</Text>
            <View style={styles.settingAction}>
              <Text style={[styles.languageText, { color: isDarkMode ? '#9BA1A6' : '#687076' }]}>
                {language === 'en' ? 'English' : 'हिन्दी'}
              </Text>
              <IconSymbol name="chevron.right" size={20} color={isDarkMode ? '#9BA1A6' : '#687076'} />
            </View>
          </TouchableOpacity>
        </View>

        {/* Features Section */}
        <Text style={[styles.sectionTitle, { color: textColor }]}>Smart Features</Text>
        <View style={[styles.card, { backgroundColor: cardColor }]}>
          <View style={styles.settingRow}>
            <View style={styles.settingLabelContainer}>
              <Text style={[styles.settingText, { color: textColor }]}>AI Analysis</Text>
              <Text style={[styles.settingDescription, { color: isDarkMode ? '#9BA1A6' : '#687076' }]}>
                Enable intelligent storage recommendations
              </Text>
            </View>
            <Switch
              value={isAIEnabled}
              onValueChange={toggleAIEnabled}
              trackColor={{ false: '#767577', true: Colors[colorScheme].tint }}
              thumbColor="#FFFFFF"
            />
          </View>
        </View>

        {/* About Section */}
        <Text style={[styles.sectionTitle, { color: textColor }]}>About</Text>
        <View style={[styles.card, { backgroundColor: cardColor }]}>
          <TouchableOpacity style={styles.settingRow}>
            <Text style={[styles.settingText, { color: textColor }]}>App Version</Text>
            <Text style={[styles.versionText, { color: isDarkMode ? '#9BA1A6' : '#687076' }]}>1.0.0</Text>
          </TouchableOpacity>
          <View style={styles.divider} />
          <TouchableOpacity style={styles.settingRow}>
            <Text style={[styles.settingText, { color: textColor }]}>Privacy Policy</Text>
            <IconSymbol name="chevron.right" size={20} color={isDarkMode ? '#9BA1A6' : '#687076'} />
          </TouchableOpacity>
          <View style={styles.divider} />
          <TouchableOpacity style={styles.settingRow}>
            <Text style={[styles.settingText, { color: textColor }]}>Terms of Service</Text>
            <IconSymbol name="chevron.right" size={20} color={isDarkMode ? '#9BA1A6' : '#687076'} />
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    paddingTop: 10,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
  },
  content: {
    padding: 16,
    paddingBottom: 30,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
    marginTop: 16,
  },
  card: {
    borderRadius: 12,
    overflow: 'hidden',
    marginBottom: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  settingRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  settingLabelContainer: {
    flex: 1,
    marginRight: 8,
  },
  settingText: {
    fontSize: 16,
    fontWeight: '500',
  },
  settingDescription: {
    fontSize: 12,
    marginTop: 4,
  },
  settingAction: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  languageText: {
    marginRight: 8,
    fontSize: 14,
  },
  versionText: {
    fontSize: 14,
  },
  divider: {
    height: 1,
    backgroundColor: '#333333',
    opacity: 0.2,
  },
  permissionStatus: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  permissionStatusText: {
    fontSize: 14,
    fontWeight: '500',
  },
  permissionButton: {
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 4,
  },
  permissionButtonText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
  },
}); 