import { useRouter } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ActivityIndicator, Alert, FlatList, SafeAreaView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';

import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useAppContext } from '@/context/AppContext';
import { FileInfo, findDuplicateFiles, findOldFiles, formatBytes, getLargeFiles, getMediaFiles } from '@/utils/storageUtils';

type AnalysisType = 'largeFiles' | 'duplicateFiles' | 'unusedApps' | 'oldFiles';

interface AIRecommendation {
  type: string;
  message: string;
  action: string;
  items: FileInfo[];
}

export default function AnalyzeScreen() {
  const { t } = useTranslation();
  const { isDarkMode, isAIEnabled } = useAppContext();
  const router = useRouter();
  
  const [analysisType, setAnalysisType] = useState<AnalysisType>('largeFiles');
  const [isLoading, setIsLoading] = useState(false);
  const [scanCompleted, setScanCompleted] = useState(false);
  const [results, setResults] = useState<FileInfo[]>([]);
  
  const colorScheme = isDarkMode ? 'dark' : 'light';
  const backgroundColor = Colors[colorScheme].background;
  const textColor = Colors[colorScheme].text;
  const accentColor = Colors[colorScheme].tint;
  
  // Function to handle scan errors with more specific messages
  const handleScanError = (errorType: string) => {
    switch (errorType) {
      case 'unusedApps':
        Alert.alert(
          'Feature Not Available',
          'This feature is coming soon in a future update.'
        );
        break;
      case 'permissionDenied':
        Alert.alert(
          'Permission Required',
          'Storage access permission is needed to analyze your files. Please grant permission in settings.'
        );
        break;
      case 'systemFiles':
        Alert.alert(
          'Protected Files',
          'Some system files cannot be analyzed for security reasons.'
        );
        break;
      default:
        Alert.alert(
          'Scan Error',
          'An error occurred while scanning. Please try again.'
        );
    }
  };
  
  useEffect(() => {
    if (scanCompleted) {
      setScanCompleted(false);
      setResults([]);
    }
  }, [analysisType]);
  
  const startScan = async () => {
    setIsLoading(true);
    setResults([]);
    setScanCompleted(false);
    
    try {
      let scanResults: FileInfo[] = [];
      
      switch (analysisType) {
        case 'largeFiles':
          scanResults = await getLargeFiles();
          break;
        case 'duplicateFiles':
          const mediaFiles = await getMediaFiles();
          if (mediaFiles.length === 0) {
            Alert.alert(
              'No Media Files Found',
              'Unable to find media files to analyze. Please check app permissions and try again.'
            );
            setIsLoading(false);
            return;
          }
          
          const duplicates = await findDuplicateFiles(mediaFiles);
          if (Object.keys(duplicates).length === 0) {
            // No duplicates found
            setResults([]);
            setIsLoading(false);
            setScanCompleted(true);
            Alert.alert(
              'No Duplicates Found',
              'Good news! No duplicate files were found in your media library.'
            );
            return;
          }
          
          scanResults = Object.values(duplicates).flat();
          break;
        case 'unusedApps':
          // App analysis requires platform-specific modules
          setTimeout(() => {
            setResults([]);
            setIsLoading(false);
            setScanCompleted(true);
            handleScanError('unusedApps');
          }, 500);
          return;
        case 'oldFiles':
          const oldMediaFiles = await getMediaFiles();
          if (oldMediaFiles.length === 0) {
            Alert.alert(
              'No Media Files Found',
              'Unable to find media files to analyze. Please check app permissions and try again.'
            );
            setIsLoading(false);
            return;
          }
          
          scanResults = findOldFiles(oldMediaFiles, 3); // Files older than 3 months
          
          if (scanResults.length === 0) {
            setResults([]);
            setIsLoading(false);
            setScanCompleted(true);
            Alert.alert(
              'No Old Files Found',
              'Good news! No files older than 3 months were found in your media library.'
            );
            return;
          }
          break;
      }
      
      // Validate results
      if (scanResults.length === 0) {
        setResults([]);
        setIsLoading(false);
        setScanCompleted(true);
        Alert.alert(
          'No Results',
          'No files matching the selected criteria were found.'
        );
        return;
      }
      
      // Sort by size, largest first
      scanResults.sort((a, b) => b.size - a.size);
      
      // Generate IDs for files that don't have them
      scanResults = scanResults.map(file => {
        if (!file.id) {
          return {
            ...file,
            id: file.uri.split('/').pop() || file.name
          };
        }
        return file;
      });
      
      setResults(scanResults);
      setIsLoading(false);
      setScanCompleted(true);
    } catch (error) {
      console.error('Scan error:', error);
      setIsLoading(false);
      Alert.alert(
        'Scan Error',
        error instanceof Error ? error.message : 'An error occurred while scanning. Please try again.'
      );
    }
  };
  
  const renderFileItem = ({ item }: { item: FileInfo }) => {
    return (
      <View style={[styles.fileItem, { backgroundColor: isDarkMode ? '#222' : '#fff' }]}>
        <IconSymbol 
          name={
            item.type === 'image' ? 'photo.fill' : 
            item.type === 'video' ? 'video.fill' : 
            'doc.fill'
          } 
          size={32} 
          color={accentColor} 
        />
        <View style={styles.fileInfo}>
          <Text style={[styles.fileName, { color: textColor }]} numberOfLines={1}>
            {item.name}
          </Text>
          <Text style={[styles.fileDetails, { color: isDarkMode ? '#aaa' : '#666' }]}>
            {formatBytes(item.size)}
          </Text>
        </View>
      </View>
    );
  };

  const handleAIAnalysis = async () => {
    if (!isAIEnabled) return;
    
    try {
      setIsLoading(true);
      
      // Get data based on current analysis type
      let data: FileInfo[] = [];
      if (analysisType === 'largeFiles') {
        data = await getLargeFiles();
      } else if (analysisType === 'duplicateFiles') {
        const mediaFiles = await getMediaFiles();
        const duplicates = await findDuplicateFiles(mediaFiles);
        data = Object.values(duplicates).flat();
      } else if (analysisType === 'oldFiles') {
        const mediaFiles = await getMediaFiles();
        data = findOldFiles(mediaFiles);
      } else if (analysisType === 'unusedApps') {
        handleScanError('unusedApps');
        setIsLoading(false);
        return;
      }
      
      // Simple client-side AI logic
      // In a real app, this could use TensorFlow.js or a similar library
      const recommendations: AIRecommendation[] = [];
      
      // Sort data by size in descending order for better recommendations
      data.sort((a, b) => b.size - a.size);
      
      // Analyze large files
      const largeFiles = data.filter(file => file.size > 100 * 1024 * 1024); // > 100MB
      if (largeFiles.length > 0) {
        recommendations.push({
          type: 'large_files',
          message: `Found ${largeFiles.length} very large files taking up ${formatBytes(largeFiles.reduce((sum, file) => sum + file.size, 0))}`,
          action: 'compress',
          items: largeFiles.slice(0, 3) // Top 3 largest
        });
      }
      
      // Analyze by type
      const imageFiles = data.filter(file => file.type === 'image');
      const videoFiles = data.filter(file => file.type === 'video');
      
      if (imageFiles.length > 10) {
        recommendations.push({
          type: 'many_images',
          message: `Found ${imageFiles.length} images that could be compressed`,
          action: 'compress_images',
          items: imageFiles.slice(0, 3) // Sample
        });
      }
      
      if (videoFiles.length > 0) {
        recommendations.push({
          type: 'videos',
          message: `Videos are using ${formatBytes(videoFiles.reduce((sum, file) => sum + file.size, 0))} of storage`,
          action: 'review_videos',
          items: videoFiles.slice(0, 3) // Sample
        });
      }
      
      // If no recommendations were found
      if (recommendations.length === 0) {
        setTimeout(() => {
          setIsLoading(false);
          Alert.alert(
            'No Recommendations',
            'No optimization opportunities were found. Your storage appears to be in good shape!',
            [{ text: 'OK', style: 'default' }]
          );
        }, 1000);
        return;
      }
      
      // Show AI recommendations dialog
      setTimeout(() => {
        setIsLoading(false);
        Alert.alert(
          'AI Recommendations',
          recommendations.map(rec => rec.message).join('\n\n'),
          [
            { text: 'Close', style: 'cancel' },
            { 
              text: 'Optimize Now', 
              onPress: () => router.push('/(tabs)/compress')
            },
          ]
        );
      }, 1500); // Simulated AI processing time
    } catch (error) {
      console.error('AI analysis error:', error);
      setIsLoading(false);
      Alert.alert(
        'AI Analysis Error',
        'An error occurred while performing AI analysis. Please try again later.'
      );
    }
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: textColor }]}>Analysis</Text>
        
        <TouchableOpacity 
          style={[styles.aiButton, { 
            backgroundColor: isDarkMode ? '#444' : '#0A7EA4',
            borderWidth: 1,
            borderColor: isDarkMode ? '#666' : '#0A7EA4',
            shadowColor: "#000",
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.1,
            shadowRadius: 4,
            elevation: 3
          }]}
          onPress={isAIEnabled ? handleAIAnalysis : () => {
            Alert.alert(
              'AI Analysis Disabled',
              'Enable AI Analysis in Settings to use this feature.',
              [
                { text: 'Cancel', style: 'cancel' },
                { 
                  text: 'Go to Settings', 
                  onPress: () => router.push('/settings')
                },
              ]
            );
          }}
        >
          <IconSymbol name="wand.and.stars" size={20} color="#FFFFFF" />
          <Text style={[styles.aiButtonText, { 
            color: '#FFFFFF',
            fontWeight: '600'
          }]}>
            Smart Analysis
          </Text>
        </TouchableOpacity>
      </View>
      
      <View style={styles.analysisTypes}>
        {[
          { id: 'largeFiles', label: 'Large Files' },
          { id: 'duplicateFiles', label: 'Duplicates' },
          { id: 'unusedApps', label: 'Unused Apps' },
          { id: 'oldFiles', label: 'Old Files' }
        ].map((type) => (
          <TouchableOpacity
            key={type.id}
            style={[
              styles.analysisButton,
              { 
                backgroundColor: isDarkMode ? 
                  (analysisType === type.id ? '#555' : '#333') : 
                  (analysisType === type.id ? '#0A7EA4' : '#DDDDDD'),
                borderWidth: 1,
                borderColor: isDarkMode ? 
                  (analysisType === type.id ? '#777' : '#444') : 
                  (analysisType === type.id ? '#0A7EA4' : '#CCCCCC')
              }
            ]}
            onPress={() => setAnalysisType(type.id as AnalysisType)}
          >
            <Text 
              style={[
                styles.analysisButtonText, 
                { 
                  color: isDarkMode ? 
                    '#FFFFFF' : 
                    (analysisType === type.id ? '#FFFFFF' : '#333333'),
                  fontWeight: '600'
                }
              ]}
            >
              {type.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
      
      <View style={styles.contentContainer}>
        {isLoading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={accentColor} />
            <Text style={[styles.loadingText, { color: textColor }]}>
              {t('analyze.scanning')}
            </Text>
          </View>
        ) : !scanCompleted ? (
          <View style={styles.startContainer}>
            <Text style={[styles.startText, { color: textColor }]}>
              Tap "Scan Now" to analyze your storage
            </Text>
            <TouchableOpacity 
              style={[styles.scanButton, { 
                backgroundColor: accentColor,
                shadowColor: "#000",
                shadowOffset: { width: 0, height: 2 },
                shadowOpacity: 0.2,
                shadowRadius: 4,
                elevation: 4,
                borderWidth: 1,
                borderColor: isDarkMode ? '#666' : '#0A7EA4'
              }]}
              onPress={startScan}
            >
              <Text style={[styles.scanButtonText, { 
                color: isDarkMode ? Colors.dark.background : '#FFFFFF',
                fontWeight: '600' 
              }]}>
                Scan Now
              </Text>
            </TouchableOpacity>
          </View>
        ) : results.length === 0 ? (
          <View style={styles.noResultsContainer}>
            <Text style={[styles.noResultsText, { color: textColor }]}>
              No results found
            </Text>
          </View>
        ) : (
          <FlatList
            data={results}
            renderItem={renderFileItem}
            keyExtractor={(item) => item.uri}
            contentContainerStyle={styles.fileList}
            showsVerticalScrollIndicator={false}
          />
        )}
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 16,
    paddingTop: 10,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  analysisTypes: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  analysisButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
    width: '48%',
  },
  analysisButtonText: {
    fontSize: 14,
  },
  contentContainer: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    fontWeight: '500',
  },
  startContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  startText: {
    marginBottom: 20,
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
  },
  scanButton: {
    paddingVertical: 14,
    paddingHorizontal: 32,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  scanButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
  },
  noResultsContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  noResultsText: {
    fontSize: 16,
  },
  fileItem: {
    flexDirection: 'row',
    padding: 12,
    borderRadius: 10,
    marginBottom: 10,
  },
  fileInfo: {
    flex: 1,
    justifyContent: 'center',
    marginLeft: 12,
  },
  fileName: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
  },
  fileDetails: {
    fontSize: 14,
  },
  aiButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 8,
    padding: 14,
    borderRadius: 10,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  aiButtonText: {
    marginLeft: 8,
    fontSize: 16,
  },
  fileList: {
    paddingHorizontal: 16,
    paddingBottom: 20,
  },
}); 